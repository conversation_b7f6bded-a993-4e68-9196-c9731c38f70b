import React, { useState } from 'react';
import { View, StyleSheet, ScrollView, Platform } from 'react-native';
import { 
  Title, 
  Button, 
  Card, 
  Paragraph, 
  TextInput,
  Divider,
  Chip
} from 'react-native-paper';
import { NotificationSettings } from '../components/NotificationSettings';
import { useNotifications } from '../context/NotificationContext';
import { useInAppNotifications } from '../context/InAppNotificationContext';

export const NotificationTestScreen: React.FC = () => {
  const { showNotification, isNotificationEnabled } = useNotifications();
  const { showNotification: showInAppNotification } = useInAppNotifications();
  const [customTitle, setCustomTitle] = useState('');
  const [customBody, setCustomBody] = useState('');

  const testNotifications = [
    {
      title: 'New Message',
      body: 'You have received a new message from Dr<PERSON> <PERSON>',
      data: { type: 'chat', senderId: '123' }
    },
    {
      title: 'Drug Reaction Alert',
      body: 'A new drug reaction has been reported and requires your attention',
      data: { type: 'reaction', reactionId: '456' }
    },
    {
      title: 'System Update',
      body: 'The ADR system has been updated with new features',
      data: { type: 'system' }
    }
  ];

  const realWorldScenarios = [
    {
      title: '💬 Simulate: Patient Message',
      body: 'John Doe: "I\'m experiencing side effects from the medication"',
      data: { type: 'chat', senderId: 'patient123', scenario: 'patient_message' }
    },
    {
      title: '⚠️ Simulate: Drug Reaction Report',
      body: 'New severe reaction to Aspirin reported by Sarah Johnson',
      data: { type: 'reaction', reactionId: 'rxn789', scenario: 'drug_reaction' }
    },
    {
      title: '🩺 Simulate: Doctor Message',
      body: 'Dr. Williams: "Please review patient\'s medication history"',
      data: { type: 'chat', senderId: 'doctor456', scenario: 'doctor_message' }
    },
    {
      title: '🚨 Simulate: Urgent Reaction',
      body: 'URGENT: Severe allergic reaction reported - immediate attention required',
      data: { type: 'reaction', severity: 'severe', scenario: 'urgent_reaction' }
    }
  ];

  const sendTestNotification = async (notification: typeof testNotifications[0]) => {
    await showNotification(notification.title, notification.body, notification.data);
  };

  const sendInAppNotification = (notification: typeof realWorldScenarios[0]) => {
    // Convert scenario to notification type
    let notificationType: 'chat' | 'drug_reaction' | 'system' | 'urgent' = 'system';

    if (notification.data.scenario?.includes('message')) {
      notificationType = 'chat';
    } else if (notification.data.scenario?.includes('reaction')) {
      notificationType = notification.data.scenario === 'urgent_reaction' ? 'urgent' : 'drug_reaction';
    }

    showInAppNotification({
      title: notification.title.replace(/^[🔔💬⚠️🩺🚨]\s*/, '').replace('Simulate: ', ''),
      body: notification.body,
      type: notificationType,
      data: notification.data,
    });
  };

  const sendCustomNotification = async () => {
    if (customTitle.trim() && customBody.trim()) {
      await showNotification(customTitle, customBody, { type: 'custom' });
      setCustomTitle('');
      setCustomBody('');
    }
  };

  const testInAppNotificationDirectly = () => {
    showInAppNotification({
      title: 'Test In-App Notification',
      body: 'This is a direct test of the in-app notification system. If you see this sliding down, the UI component works!',
      type: 'system',
      data: { test: true },
    });
  };

  const testSocketConnection = () => {
    // This will test if we can send a socket event to ourselves
    showInAppNotification({
      title: 'Socket Test',
      body: 'Testing socket connection to backend server...',
      type: 'system',
      data: { test: true },
    });

    // Try to emit a test event (this requires backend to be running)
    console.log('🧪 Testing socket connection...');
  };

  const testBrowserNotificationDirectly = async () => {
    // Test browser notification directly without our service
    if ('Notification' in window) {
      const permission = await Notification.requestPermission();
      if (permission === 'granted') {
        new Notification('Direct Browser Test', {
          body: 'This is a direct browser notification test - if you see this, your browser supports notifications!',
          icon: '/favicon.png'
        });
      } else {
        alert(`Notification permission: ${permission}`);
      }
    } else {
      alert('This browser does not support notifications');
    }
  };

  return (
    <ScrollView style={styles.container}>
      <Title style={styles.title}>Notification Testing</Title>
      
      <Paragraph style={styles.description}>
        This screen allows you to test the notification system. 
        {Platform.OS === 'web' 
          ? ' On web, notifications will appear as browser notifications.'
          : ' On mobile, notifications will appear as push notifications.'
        }
      </Paragraph>

      {/* Notification Settings */}
      <NotificationSettings />

      {/* Real-World Scenario Simulations */}
      <Card style={styles.card}>
        <Card.Content>
          <Title>🎯 Real-World Scenarios</Title>
          <Paragraph style={styles.cardDescription}>
            These simulate the exact notifications you'd receive when real events happen:
          </Paragraph>

          {realWorldScenarios.map((notification, index) => (
            <View key={index} style={styles.testItem}>
              <View style={styles.testContent}>
                <Paragraph style={styles.testTitle}>{notification.title}</Paragraph>
                <Paragraph style={styles.testBody}>{notification.body}</Paragraph>
                <Chip
                  mode="outlined"
                  style={styles.typeChip}
                  textStyle={styles.chipText}
                >
                  {notification.data.scenario}
                </Chip>
              </View>
              <Button
                mode="contained"
                onPress={() => sendTestNotification(notification)}
                disabled={!isNotificationEnabled}
                style={styles.testButton}
                buttonColor="#FF6B35"
              >
                Simulate
              </Button>
            </View>
          ))}
        </Card.Content>
      </Card>

      {/* Basic Test Notifications */}
      <Card style={styles.card}>
        <Card.Content>
          <Title>Basic Test Notifications</Title>
          <Paragraph style={styles.cardDescription}>
            Simple test notifications to verify the system works:
          </Paragraph>

          {testNotifications.map((notification, index) => (
            <View key={index} style={styles.testItem}>
              <View style={styles.testContent}>
                <Paragraph style={styles.testTitle}>{notification.title}</Paragraph>
                <Paragraph style={styles.testBody}>{notification.body}</Paragraph>
                <Chip
                  mode="outlined"
                  style={styles.typeChip}
                  textStyle={styles.chipText}
                >
                  {notification.data.type}
                </Chip>
              </View>
              <Button
                mode="contained"
                onPress={() => sendTestNotification(notification)}
                disabled={!isNotificationEnabled}
                style={styles.testButton}
              >
                Send
              </Button>
            </View>
          ))}
        </Card.Content>
      </Card>

      {/* Custom Notification */}
      <Card style={styles.card}>
        <Card.Content>
          <Title>Custom Notification</Title>
          <Paragraph style={styles.cardDescription}>
            Create your own notification:
          </Paragraph>

          <TextInput
            label="Notification Title"
            value={customTitle}
            onChangeText={setCustomTitle}
            style={styles.input}
            mode="outlined"
          />

          <TextInput
            label="Notification Body"
            value={customBody}
            onChangeText={setCustomBody}
            style={styles.input}
            mode="outlined"
            multiline
            numberOfLines={3}
          />

          <Button
            mode="contained"
            onPress={sendCustomNotification}
            disabled={!isNotificationEnabled || !customTitle.trim() || !customBody.trim()}
            style={styles.sendButton}
          >
            Send Custom Notification
          </Button>
        </Card.Content>
      </Card>

      {/* In-App Notification Test */}
      <Card style={styles.card}>
        <Card.Content>
          <Title>📱 In-App Notifications (No Permission Required)</Title>
          <Paragraph style={styles.cardDescription}>
            These notifications appear directly in the app without requiring browser permissions:
          </Paragraph>

          {realWorldScenarios.map((notification, index) => (
            <View key={`inapp-${index}`} style={styles.testItem}>
              <View style={styles.testContent}>
                <Paragraph style={styles.testTitle}>{notification.title}</Paragraph>
                <Paragraph style={styles.testBody}>{notification.body}</Paragraph>
                <Chip
                  mode="outlined"
                  style={styles.typeChip}
                  textStyle={styles.chipText}
                >
                  in-app
                </Chip>
              </View>
              <Button
                mode="contained"
                onPress={() => sendInAppNotification(notification)}
                style={styles.testButton}
                buttonColor="#4CAF50"
              >
                Show In-App
              </Button>
            </View>
          ))}
        </Card.Content>
      </Card>

      {/* Direct In-App Notification Test */}
      <Card style={styles.card}>
        <Card.Content>
          <Title>🧪 Direct UI Test</Title>
          <Paragraph style={styles.cardDescription}>
            Test if the in-app notification UI component works:
          </Paragraph>
          <Button
            mode="contained"
            icon="bell-ring"
            onPress={testInAppNotificationDirectly}
            style={styles.testButton}
            buttonColor="#9C27B0"
          >
            Test In-App Notification UI
          </Button>
        </Card.Content>
      </Card>

      {/* Browser Notification Test */}
      <Card style={styles.card}>
        <Card.Content>
          <Title>🔧 Browser Test</Title>
          <Paragraph style={styles.cardDescription}>
            Test if your browser supports notifications directly:
          </Paragraph>
          <Button
            mode="outlined"
            icon="test-tube"
            onPress={testBrowserNotificationDirectly}
            style={styles.testButton}
          >
            Test Browser Notifications Directly
          </Button>
        </Card.Content>
      </Card>

      {/* Status Information */}
      <Card style={styles.card}>
        <Card.Content>
          <Title>System Status</Title>
          <View style={styles.statusRow}>
            <Paragraph>Platform:</Paragraph>
            <Chip mode="outlined">{Platform.OS}</Chip>
          </View>
          <View style={styles.statusRow}>
            <Paragraph>Notifications:</Paragraph>
            <Chip
              mode="outlined"
              style={isNotificationEnabled ? styles.enabledChip : styles.disabledChip}
            >
              {isNotificationEnabled ? 'Enabled' : 'Disabled'}
            </Chip>
          </View>
        </Card.Content>
      </Card>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  title: {
    textAlign: 'center',
    marginVertical: 16,
  },
  description: {
    margin: 16,
    textAlign: 'center',
    color: '#666',
  },
  card: {
    margin: 16,
  },
  cardDescription: {
    marginBottom: 16,
    color: '#666',
  },
  testItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
    padding: 12,
    backgroundColor: '#f9f9f9',
    borderRadius: 8,
  },
  testContent: {
    flex: 1,
  },
  testTitle: {
    fontWeight: 'bold',
    marginBottom: 4,
  },
  testBody: {
    color: '#666',
    marginBottom: 8,
  },
  typeChip: {
    alignSelf: 'flex-start',
  },
  chipText: {
    fontSize: 12,
  },
  testButton: {
    marginLeft: 12,
  },
  input: {
    marginBottom: 12,
  },
  sendButton: {
    marginTop: 8,
  },
  statusRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  enabledChip: {
    backgroundColor: '#e8f5e8',
  },
  disabledChip: {
    backgroundColor: '#ffeaea',
  },
});
