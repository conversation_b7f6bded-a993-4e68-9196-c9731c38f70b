import React from 'react';
import { createNativeStackNavigator } from '@react-navigation/native-stack';
import { IconButton } from 'react-native-paper';
import { UserDashboard } from '../screens/user/UserDashboard';
import { DoctorDashboard } from '../screens/doctor/DoctorDashboard';
import { CounsellorDashboard } from '../screens/counsellor/CounsellorDashboard';
import { AdminDashboard } from '../screens/admin/AdminDashboard';
import { StaffManagement } from '../screens/admin/StaffManagement';
import { PatientManagement } from '../screens/admin/PatientManagement';
import { Analytics } from '../screens/admin/Analytics';
import { ReportReactionScreen } from '../screens/user/ReportReactionScreen';
import ViewReactionsScreen from '../screens/user/ViewReactionsScreen';
import { ReactionManagementScreen } from '../screens/counsellor/ReactionManagementScreen';
import { PatientListScreen } from '../screens/common/PatientListScreen';
import { PatientProfileScreen } from '../screens/common/PatientProfileScreen';
import PatientChatScreen from '../screens/user/PatientChatScreen';
import CounsellorChatScreen from '../screens/counsellor/CounsellorChatScreen';
import DrugCounsellingScreen from '../screens/counsellor/DrugCounsellingScreen';
import { theme } from '../theme';
import { EditCredentialsScreen } from '../screens/user/EditCredentialsScreen';
import { AddPatientScreen } from '../screens/doctor/AddPatientScreen';
import { DoctorChatScreen } from '../screens/doctor/DoctorChatScreen';
import { PatientReportsScreen } from '../screens/doctor/PatientReportsScreen';
import { CounsellorDoctorChat } from '../screens/counsellor/CounsellorDoctorChat';
import { NotificationTestScreen } from '../screens/NotificationTestScreen';

const Stack = createNativeStackNavigator();

interface MainNavigatorProps {
  userRole: 'patient' | 'doctor' | 'drug-counsellor' | 'admin';
}

export const MainNavigator = ({ userRole }: MainNavigatorProps) => {
  return (
    <Stack.Navigator
      screenOptions={{
        headerStyle: {
          backgroundColor: theme.colors.primary,
        },
        headerTintColor: '#fff',
        headerTitleStyle: {
          fontWeight: 'bold',
        },
      }}
      initialRouteName={
        userRole === 'patient' ? 'UserDashboard' :
        userRole === 'doctor' ? 'DoctorDashboard' :
        userRole === 'drug-counsellor' ? 'CounsellorDashboard' :
        'AdminDashboard'
      }
    >
      <Stack.Screen
        name="CounsellorChat"
        component={CounsellorChatScreen as React.ComponentType<any>}
        options={({ route, navigation }) => ({
          title: userRole === 'patient' ? 'Patient Chats' : 'Chat with Patient',
          headerLeft: () => (
            <IconButton
              icon="arrow-left"
              iconColor={theme.colors.surface}
              onPress={() => navigation.goBack()}
            />
          ),
        })}
      />

      {userRole === 'patient' && (
        <>
          <Stack.Screen
            name="UserDashboard"
            component={UserDashboard}
            options={{ headerShown: false }}
          />
          <Stack.Screen
            name="ReportReaction"
            component={ReportReactionScreen}
            options={({ navigation }) => ({
              title: 'Report Drug Reaction',
              headerLeft: () => (
                <IconButton
                  icon="arrow-left"
                  iconColor={theme.colors.surface}
                  onPress={() => navigation.goBack()}
                />
              ),
            })}
          />
          <Stack.Screen
            name="ViewReactions"
            component={ViewReactionsScreen}
            options={({ navigation }) => ({
              title: 'Your Reported Reactions',
              headerLeft: () => (
                <IconButton
                  icon="arrow-left"
                  iconColor={theme.colors.surface}
                  onPress={() => navigation.goBack()}
                />
              ),
            })}
          />
          <Stack.Screen
            name="PatientProfile"
            component={PatientProfileScreen}
            options={{ headerShown: false }}
          />
          <Stack.Screen
            name="PatientChat"
            component={PatientChatScreen}
            options={({ navigation }) => ({
              title: 'Chat with Counsellor',
              headerLeft: () => (
                <IconButton
                  icon="arrow-left"
                  iconColor={theme.colors.surface}
                  onPress={() => navigation.goBack()}
                />
              ),
            })}
            initialParams={{ counselorId: 'default' }}
          />
          <Stack.Screen
          name="EditProfile"
          component={EditCredentialsScreen}
          options={{headerShown:false}}
          />
          <Stack.Screen
            name="NotificationTest"
            component={NotificationTestScreen}
            options={({ navigation }) => ({
              title: 'Notification Testing',
              headerLeft: () => (
                <IconButton
                  icon="arrow-left"
                  iconColor={theme.colors.surface}
                  onPress={() => navigation.goBack()}
                />
              ),
            })}
          />
        </>
      )}

      {userRole === 'doctor' && (
        <>
          <Stack.Screen
            name="DoctorDashboard"
            component={DoctorDashboard}
            options={{ headerShown: false }}
          />
          <Stack.Screen
            name="AddPatient"
            component={AddPatientScreen}
            options={{ headerShown: false }}
          />
          <Stack.Screen
            name="DoctorChat"
            component={DoctorChatScreen}
            options={({ navigation }) => ({
              title: 'Chat with Drug Counsellor',
              headerLeft: () => (
                <IconButton
                  icon="arrow-left"
                  iconColor={theme.colors.surface}
                  onPress={() => navigation.goBack()}
                />
              ),
            })}
          />
          <Stack.Screen
            name="PatientList"
            component={PatientListScreen}
            options={{ headerShown: false }}
          />
          <Stack.Screen
            name="PatientProfile"
            component={PatientProfileScreen}
            options={{ headerShown: false }}
          />
          <Stack.Screen
            name="PatientReports"
            component={PatientReportsScreen}
            options={{ headerShown: false }}
          />
        </>
      )}

      {userRole === 'drug-counsellor' && (
        <>
          <Stack.Screen
            name="CounsellorDashboard"
            component={CounsellorDashboard}
            options={{ headerShown: false }}
          />
          <Stack.Screen
            name="ReactionManagement"
            component={ReactionManagementScreen}
            options={({ navigation }) => ({
              title: 'Manage Reactions',
              headerLeft: () => (
                <IconButton
                  icon="arrow-left"
                  iconColor={theme.colors.surface}
                  onPress={() => navigation.goBack()}
                />
              ),
            })}
          />
          <Stack.Screen
            name="PatientList"
            component={PatientListScreen}
            options={({ navigation }) => ({
              headerShown: true,
              headerLeft: () => (
                <IconButton
                  icon="arrow-left"
                  iconColor={theme.colors.surface}
                  onPress={() => navigation.goBack()}
                />
              ),
            })}
          />
          <Stack.Screen
            name="PatientProfile"
            component={PatientProfileScreen}
            options={({ navigation }) => ({
              headerShown: true,
              headerLeft: () => (
                <IconButton
                  icon="arrow-left"
                  iconColor={theme.colors.surface}
                  onPress={() => navigation.goBack()}
                />
              ),
            })}
          />
          <Stack.Screen
            name="DrugCounselling"
            component={DrugCounsellingScreen}
            options={({ navigation }) => ({
              title: 'Drug Counselling',
              headerLeft: () => (
                <IconButton
                  icon="arrow-left"
                  iconColor={theme.colors.surface}
                  onPress={() => navigation.goBack()}
                />
              ),
            })}
          />
          <Stack.Screen
            name="CounsellorDoctorChat"
            component={CounsellorDoctorChat}
            options={({ navigation }) => ({
              title: 'Chat with Doctors',
              headerStyle: {
                backgroundColor: theme.colors.primary,
              },
              headerTintColor: '#fff',
              headerLeft: () => (
                <IconButton
                  icon="arrow-left"
                  iconColor={theme.colors.surface}
                  onPress={() => navigation.goBack()}
                />
              ),
            })}
          />
        </>
      )}

      {userRole === 'admin' && (
        <>
          <Stack.Screen
            name="AdminDashboard"
            component={AdminDashboard}
            options={{ headerShown: false }}
          />
          <Stack.Screen
            name="StaffManagement"
            component={StaffManagement}
            options={({ route, navigation }: any) => ({
              title: `${route.params?.staffType === 'doctor' ? 'Doctors' : 'Drug Counsellors'} Management`,
              headerLeft: () => (
                <IconButton
                  icon="arrow-left"
                  iconColor={theme.colors.surface}
                  onPress={() => navigation.goBack()}
                />
              ),
            })}
          />
          <Stack.Screen
            name="PatientManagement"
            component={PatientManagement}
            options={({ navigation }) => ({
              title: ' Patient Management',
              headerShown: true,
              headerLeft: () => (
                <IconButton
                  icon="arrow-left"
                  iconColor={theme.colors.surface}
                  onPress={() => navigation.goBack()}
                />
              ),
            })}
          />
          <Stack.Screen
            name="Analytics"
            component={Analytics}
            options={({ navigation }) => ({
              headerShown: true,
              headerLeft: () => (
                <IconButton
                  icon="arrow-left"
                  iconColor={theme.colors.surface}
                  onPress={() => navigation.goBack()}
                />
              ),
            })}
          />
          <Stack.Screen
            name="NotificationTest"
            component={NotificationTestScreen}
            options={({ navigation }) => ({
              title: 'Notification Testing',
              headerLeft: () => (
                <IconButton
                  icon="arrow-left"
                  iconColor={theme.colors.surface}
                  onPress={() => navigation.goBack()}
                />
              ),
            })}
          />
        </>
      )}
    </Stack.Navigator>
  );
};
