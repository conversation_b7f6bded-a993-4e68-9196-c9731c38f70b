import React from 'react';
import { InAppNotification } from './InAppNotification';
import { useInAppNotifications } from '../context/InAppNotificationContext';

export const InAppNotificationContainer: React.FC = () => {
  const { currentNotification, dismissNotification } = useInAppNotifications();

  const handleNotificationTap = (notification: any) => {
    console.log('Notification tapped:', notification);
    // You can add navigation logic here based on notification type
    // For example:
    // if (notification.type === 'chat') {
    //   navigation.navigate('Chat', { chatId: notification.data.chatId });
    // }
  };

  return (
    <InAppNotification
      notification={currentNotification}
      onDismiss={dismissNotification}
      onTap={handleNotificationTap}
    />
  );
};
