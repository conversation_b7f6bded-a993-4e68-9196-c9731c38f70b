import React, { useState, useEffect } from 'react';
import { View, StyleSheet, Animated, Dimensions } from 'react-native';
import { Card, Text, IconButton, Avatar } from 'react-native-paper';
import { Platform } from 'react-native';

interface NotificationData {
  id: string;
  title: string;
  body: string;
  type: 'chat' | 'drug_reaction' | 'system' | 'urgent';
  timestamp: Date;
  data?: any;
}

interface InAppNotificationProps {
  notification: NotificationData | null;
  onDismiss: () => void;
  onTap?: (notification: NotificationData) => void;
}

export const InAppNotification: React.FC<InAppNotificationProps> = ({
  notification,
  onDismiss,
  onTap,
}) => {
  const [slideAnim] = useState(new Animated.Value(-100));
  const [visible, setVisible] = useState(false);

  useEffect(() => {
    if (notification) {
      setVisible(true);
      // Slide in
      Animated.spring(slideAnim, {
        toValue: 0,
        useNativeDriver: true,
        tension: 100,
        friction: 8,
      }).start();

      // Auto dismiss after 5 seconds (except for urgent notifications)
      if (notification.type !== 'urgent') {
        const timer = setTimeout(() => {
          handleDismiss();
        }, 5000);

        return () => clearTimeout(timer);
      }
    }
  }, [notification]);

  const handleDismiss = () => {
    // Slide out
    Animated.timing(slideAnim, {
      toValue: -100,
      duration: 300,
      useNativeDriver: true,
    }).start(() => {
      setVisible(false);
      onDismiss();
    });
  };

  const handleTap = () => {
    if (notification && onTap) {
      onTap(notification);
    }
    handleDismiss();
  };

  const getNotificationIcon = (type: string) => {
    switch (type) {
      case 'chat':
        return 'message-text';
      case 'drug_reaction':
        return 'alert-circle';
      case 'urgent':
        return 'alert-octagon';
      case 'system':
        return 'information';
      default:
        return 'bell';
    }
  };

  const getNotificationColor = (type: string) => {
    switch (type) {
      case 'chat':
        return '#2196F3';
      case 'drug_reaction':
        return '#FF9800';
      case 'urgent':
        return '#F44336';
      case 'system':
        return '#4CAF50';
      default:
        return '#757575';
    }
  };

  if (!visible || !notification) {
    return null;
  }

  return (
    <Animated.View
      style={[
        styles.container,
        {
          transform: [{ translateY: slideAnim }],
        },
      ]}
    >
      <Card
        style={[
          styles.card,
          notification.type === 'urgent' && styles.urgentCard,
        ]}
        onPress={handleTap}
      >
        <View style={styles.content}>
          <View style={styles.iconContainer}>
            <Avatar.Icon
              size={40}
              icon={getNotificationIcon(notification.type)}
              style={[
                styles.icon,
                { backgroundColor: getNotificationColor(notification.type) },
              ]}
            />
          </View>
          
          <View style={styles.textContainer}>
            <Text style={styles.title} numberOfLines={1}>
              {notification.title}
            </Text>
            <Text style={styles.body} numberOfLines={2}>
              {notification.body}
            </Text>
            <Text style={styles.timestamp}>
              {notification.timestamp.toLocaleTimeString()}
            </Text>
          </View>

          <IconButton
            icon="close"
            size={20}
            onPress={handleDismiss}
            style={styles.closeButton}
          />
        </View>

        {notification.type === 'urgent' && (
          <View style={styles.urgentIndicator}>
            <Text style={styles.urgentText}>URGENT</Text>
          </View>
        )}
      </Card>
    </Animated.View>
  );
};

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    top: Platform.OS === 'web' ? 20 : 50,
    left: 16,
    right: 16,
    zIndex: 9999,
    elevation: 10,
  },
  card: {
    backgroundColor: '#fff',
    borderRadius: 12,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 8,
  },
  urgentCard: {
    borderLeftWidth: 4,
    borderLeftColor: '#F44336',
  },
  content: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
  },
  iconContainer: {
    marginRight: 12,
  },
  icon: {
    margin: 0,
  },
  textContainer: {
    flex: 1,
  },
  title: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 4,
  },
  body: {
    fontSize: 14,
    color: '#666',
    marginBottom: 4,
  },
  timestamp: {
    fontSize: 12,
    color: '#999',
  },
  closeButton: {
    margin: 0,
  },
  urgentIndicator: {
    backgroundColor: '#F44336',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderBottomLeftRadius: 12,
    borderBottomRightRadius: 12,
  },
  urgentText: {
    color: '#fff',
    fontSize: 12,
    fontWeight: 'bold',
    textAlign: 'center',
  },
});
