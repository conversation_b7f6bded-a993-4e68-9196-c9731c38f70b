import { Platform } from 'react-native';
import * as Notifications from 'expo-notifications';
import * as Device from 'expo-device';
import Constants from 'expo-constants';

// Configure notification behavior
Notifications.setNotificationHandler({
  handleNotification: async () => ({
    shouldShowAlert: true,
    shouldPlaySound: true,
    shouldSetBadge: false,
    shouldShowBanner: true,
    shouldShowList: true,
  }),
});

export interface NotificationService {
  requestPermissions(): Promise<boolean>;
  getExpoPushToken(): Promise<string | null>;
  registerForPushNotifications(): Promise<string | null>;
  scheduleLocalNotification(title: string, body: string, data?: any): Promise<void>;
  addNotificationListener(listener: (notification: any) => void): () => void;
  addNotificationResponseListener(listener: (response: any) => void): () => void;
}

class ExpoNotificationService implements NotificationService {
  async requestPermissions(): Promise<boolean> {
    try {
      if (Platform.OS === 'web') {
        // For web, we'll use browser notifications if available
        if ('Notification' in window) {
          const permission = await Notification.requestPermission();
          return permission === 'granted';
        }
        return false;
      }

      // For mobile platforms
      if (Device.isDevice) {
        const { status: existingStatus } = await Notifications.getPermissionsAsync();
        let finalStatus = existingStatus;
        
        if (existingStatus !== 'granted') {
          const { status } = await Notifications.requestPermissionsAsync();
          finalStatus = status;
        }
        
        return finalStatus === 'granted';
      } else {
        console.log('Must use physical device for Push Notifications');
        return false;
      }
    } catch (error) {
      console.error('Error requesting notification permissions:', error);
      return false;
    }
  }

  async getExpoPushToken(): Promise<string | null> {
    try {
      if (Platform.OS === 'web') {
        // Web doesn't support Expo push tokens
        console.log('Expo push tokens not supported on web');
        return null;
      }

      if (!Device.isDevice) {
        console.log('Must use physical device for Push Notifications');
        return null;
      }

      const token = await Notifications.getExpoPushTokenAsync({
        projectId: Constants.expoConfig?.extra?.eas?.projectId,
      });
      
      console.log('Expo push token:', token.data);
      return token.data;
    } catch (error) {
      console.error('Error getting Expo push token:', error);
      return null;
    }
  }

  async registerForPushNotifications(): Promise<string | null> {
    try {
      const hasPermission = await this.requestPermissions();
      if (!hasPermission) {
        console.log('Notification permissions not granted');
        return null;
      }

      const token = await this.getExpoPushToken();
      return token;
    } catch (error) {
      console.error('Error registering for push notifications:', error);
      return null;
    }
  }

  async scheduleLocalNotification(title: string, body: string, data?: any): Promise<void> {
    try {
      if (Platform.OS === 'web') {
        // Use browser notifications for web
        if ('Notification' in window && Notification.permission === 'granted') {
          new Notification(title, {
            body,
            icon: '/favicon.png',
            data,
          });
        }
        return;
      }

      // Use Expo notifications for mobile
      await Notifications.scheduleNotificationAsync({
        content: {
          title,
          body,
          data,
        },
        trigger: null, // Show immediately
      });
    } catch (error) {
      console.error('Error scheduling local notification:', error);
    }
  }

  addNotificationListener(listener: (notification: any) => void): () => void {
    if (Platform.OS === 'web') {
      // For web, we can't easily listen to notifications, so return a no-op
      return () => {};
    }

    const subscription = Notifications.addNotificationReceivedListener(listener);
    return () => subscription.remove();
  }

  addNotificationResponseListener(listener: (response: any) => void): () => void {
    if (Platform.OS === 'web') {
      // For web, we can't easily listen to notification responses, so return a no-op
      return () => {};
    }

    const subscription = Notifications.addNotificationResponseReceivedListener(listener);
    return () => subscription.remove();
  }
}

// Web-compatible fallback service
class WebNotificationService implements NotificationService {
  async requestPermissions(): Promise<boolean> {
    if ('Notification' in window) {
      const permission = await Notification.requestPermission();
      return permission === 'granted';
    }
    return false;
  }

  async getExpoPushToken(): Promise<string | null> {
    console.log('Expo push tokens not supported on web');
    return null;
  }

  async registerForPushNotifications(): Promise<string | null> {
    const hasPermission = await this.requestPermissions();
    if (hasPermission) {
      console.log('Web notifications enabled');
    }
    return null; // Web doesn't use Expo push tokens
  }

  async scheduleLocalNotification(title: string, body: string, data?: any): Promise<void> {
    if ('Notification' in window && Notification.permission === 'granted') {
      new Notification(title, {
        body,
        icon: '/favicon.png',
        data,
      });
    }
  }

  addNotificationListener(listener: (notification: any) => void): () => void {
    return () => {}; // No-op for web
  }

  addNotificationResponseListener(listener: (response: any) => void): () => void {
    return () => {}; // No-op for web
  }
}

// Export the appropriate service based on platform
export const notificationService: NotificationService = Platform.OS === 'web' 
  ? new WebNotificationService() 
  : new ExpoNotificationService();

// Utility functions
export const showLocalNotification = async (title: string, body: string, data?: any) => {
  await notificationService.scheduleLocalNotification(title, body, data);
};

export const requestNotificationPermissions = async (): Promise<boolean> => {
  return await notificationService.requestPermissions();
};

export const registerForPushNotifications = async (): Promise<string | null> => {
  return await notificationService.registerForPushNotifications();
};
