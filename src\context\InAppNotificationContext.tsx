import React, { createContext, useContext, useState, ReactNode, useEffect } from 'react';
import { io, Socket } from 'socket.io-client';
import { useAuth } from './AuthContext';

interface NotificationData {
  id: string;
  title: string;
  body: string;
  type: 'chat' | 'drug_reaction' | 'system' | 'urgent';
  timestamp: Date;
  data?: any;
}

interface InAppNotificationContextType {
  showNotification: (notification: Omit<NotificationData, 'id' | 'timestamp'>) => void;
  currentNotification: NotificationData | null;
  dismissNotification: () => void;
  notificationHistory: NotificationData[];
}

const InAppNotificationContext = createContext<InAppNotificationContextType | undefined>(undefined);

interface InAppNotificationProviderProps {
  children: ReactNode;
}

export const InAppNotificationProvider: React.FC<InAppNotificationProviderProps> = ({ children }) => {
  const [currentNotification, setCurrentNotification] = useState<NotificationData | null>(null);
  const [notificationHistory, setNotificationHistory] = useState<NotificationData[]>([]);
  const [socket, setSocket] = useState<Socket | null>(null);
  const { user } = useAuth();

  const showNotification = (notification: Omit<NotificationData, 'id' | 'timestamp'>) => {
    const newNotification: NotificationData = {
      ...notification,
      id: Date.now().toString(),
      timestamp: new Date(),
    };

    // Add to history
    setNotificationHistory(prev => [newNotification, ...prev.slice(0, 49)]); // Keep last 50

    // Show current notification
    setCurrentNotification(newNotification);

    console.log('🔔 In-app notification shown:', newNotification.title);
  };

  const dismissNotification = () => {
    setCurrentNotification(null);
  };

  // Socket connection and real-time notification listening
  useEffect(() => {
    console.log('🔍 InAppNotificationContext user check:', user);
    if (user && (user.id || user._id)) {
      const userId = user.id || user._id;
      console.log('🔌 Connecting to socket for user:', userId);
      // Connect to socket when user is authenticated
      const newSocket = io(process.env.EXPO_PUBLIC_API_URL || 'http://localhost:5000');
      setSocket(newSocket);

      // Join user's room for personalized notifications
      newSocket.emit('join', userId);
      console.log('📡 Joined socket room for user:', userId);

      // Add connection event listeners
      newSocket.on('connect', () => {
        console.log('✅ Socket connected successfully');
      });

      newSocket.on('disconnect', () => {
        console.log('❌ Socket disconnected');
      });

      // Listen for real-time notifications
      newSocket.on('notification', (notificationData: any) => {
        console.log('🔔 Real-time notification received:', notificationData);

        // Convert backend notification to in-app notification
        let notificationType: 'chat' | 'drug_reaction' | 'system' | 'urgent' = 'system';

        if (notificationData.type === 'chat') {
          notificationType = 'chat';
        } else if (notificationData.type === 'drug_reaction') {
          notificationType = notificationData.urgent ? 'urgent' : 'drug_reaction';
        }

        showNotification({
          title: notificationData.title,
          body: notificationData.body,
          type: notificationType,
          data: notificationData.data || {},
        });
      });

      // Listen for chat messages (existing socket event)
      newSocket.on('receive-message', (messageData: any) => {
        // Only show notification if the message is for this user
        if (messageData.receiverId === userId) {
          showNotification({
            title: `New message from ${messageData.senderName || 'Unknown'}`,
            body: messageData.content.length > 50
              ? `${messageData.content.substring(0, 47)}...`
              : messageData.content,
            type: 'chat',
            data: { messageId: messageData.id, senderId: messageData.senderId },
          });
        }
      });

      return () => {
        newSocket.disconnect();
        console.log('🔌 Socket disconnected for user:', userId);
      };
    } else {
      console.log('⏳ Waiting for user authentication before connecting socket');
    }
  }, [user]);

  const value: InAppNotificationContextType = {
    showNotification,
    currentNotification,
    dismissNotification,
    notificationHistory,
  };

  return (
    <InAppNotificationContext.Provider value={value}>
      {children}
    </InAppNotificationContext.Provider>
  );
};

export const useInAppNotifications = (): InAppNotificationContextType => {
  const context = useContext(InAppNotificationContext);
  if (context === undefined) {
    throw new Error('useInAppNotifications must be used within an InAppNotificationProvider');
  }
  return context;
};
