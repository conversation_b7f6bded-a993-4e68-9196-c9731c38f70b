import React, { createContext, useContext, useState, ReactNode } from 'react';

interface NotificationData {
  id: string;
  title: string;
  body: string;
  type: 'chat' | 'drug_reaction' | 'system' | 'urgent';
  timestamp: Date;
  data?: any;
}

interface InAppNotificationContextType {
  showNotification: (notification: Omit<NotificationData, 'id' | 'timestamp'>) => void;
  currentNotification: NotificationData | null;
  dismissNotification: () => void;
  notificationHistory: NotificationData[];
}

const InAppNotificationContext = createContext<InAppNotificationContextType | undefined>(undefined);

interface InAppNotificationProviderProps {
  children: ReactNode;
}

export const InAppNotificationProvider: React.FC<InAppNotificationProviderProps> = ({ children }) => {
  const [currentNotification, setCurrentNotification] = useState<NotificationData | null>(null);
  const [notificationHistory, setNotificationHistory] = useState<NotificationData[]>([]);

  const showNotification = (notification: Omit<NotificationData, 'id' | 'timestamp'>) => {
    const newNotification: NotificationData = {
      ...notification,
      id: Date.now().toString(),
      timestamp: new Date(),
    };

    // Add to history
    setNotificationHistory(prev => [newNotification, ...prev.slice(0, 49)]); // Keep last 50

    // Show current notification
    setCurrentNotification(newNotification);

    console.log('🔔 In-app notification shown:', newNotification.title);
  };

  const dismissNotification = () => {
    setCurrentNotification(null);
  };

  const value: InAppNotificationContextType = {
    showNotification,
    currentNotification,
    dismissNotification,
    notificationHistory,
  };

  return (
    <InAppNotificationContext.Provider value={value}>
      {children}
    </InAppNotificationContext.Provider>
  );
};

export const useInAppNotifications = (): InAppNotificationContextType => {
  const context = useContext(InAppNotificationContext);
  if (context === undefined) {
    throw new Error('useInAppNotifications must be used within an InAppNotificationProvider');
  }
  return context;
};
