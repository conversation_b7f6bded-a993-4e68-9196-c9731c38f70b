import React, { useState, useEffect } from 'react';
import { View, StyleSheet, ScrollView, Dimensions } from 'react-native';
import { Text, Button, Card, IconButton, Surface, Dialog, Portal, Paragraph, Avatar, useTheme } from 'react-native-paper';
import { theme, styles as globalStyles } from '../../theme';
import { useAuth, API_BASE_URL } from '../../context/AuthContext';
import type { User } from '../../types';
import { useIsFocused } from '@react-navigation/native';

// Simple responsive breakpoint - consistent with other dashboard components
const SCREEN_BREAKPOINT_TABLET = 768;

export const AdminDashboard = ({ navigation }: any) => {
  const { user, logout } = useAuth() as { user: User | null; logout: () => void; };
  const paperTheme = useTheme(); // Add useTheme hook for accessing theme in the component
  const isFocused = useIsFocused();
  const [screenWidth, setScreenWidth] = useState(Dimensions.get('window').width);
  const [logoutDialogVisible, setLogoutDialogVisible] = useState(false);
  const [stats, setStats] = useState({
    totalDoctors: 0,
    totalCounsellors: 0,
    totalPatients: 0,
  });

  // Update screen width when dimensions change
  useEffect(() => {
    const updateLayout = () => {
      setScreenWidth(Dimensions.get('window').width);
    };

    // Add event listener for dimension changes
    const subscription = Dimensions.addEventListener('change', updateLayout);

    // Clean up event listener on component unmount
    return () => subscription.remove();
  }, []);

  // Function to show the logout confirmation dialog
  const showLogoutDialog = () => {
    setLogoutDialogVisible(true);
  };

  // Function to handle logout confirmation
  const handleLogoutConfirm = async () => {
    setLogoutDialogVisible(false);
    await logout();
  };

  // Function to dismiss the logout dialog
  const hideLogoutDialog = () => {
    setLogoutDialogVisible(false);
  };

  const fetchUserData = async () => {
    try {
      const response = await fetch(`${API_BASE_URL}/api/staff/counts`);
      const counts = await response.json();

      setStats({
        totalDoctors: counts.totalDoctors,
        totalCounsellors: counts.totalCounsellors,
        totalPatients: counts.totalPatients,
      });
    } catch (error) {
      console.error('Error fetching user data:', error);
    }
  };


  React.useEffect(() => {
    if (isFocused) {
      fetchUserData();
    }
  }, [isFocused]);

  const navigateToStaffManagement = (staffType: 'doctor' | 'drug-counsellor') => {
    navigation.navigate('StaffManagement', {
      staffType,
      title: `${staffType === 'doctor' ? 'Doctors' : 'Drug Counsellors'} Management`
    });
  };

  const navigateToPatientManagement = () => {
    navigation.navigate('PatientManagement');
  };

  return (
    <ScrollView style={styles.container}>
      <View style={styles.header}>
        <View>
          <Text style={styles.title}>Admin Dashboard</Text>
          <Text style={styles.subtitle}>Welcome, {user && typeof user.name === 'string' ? user.name : 'Guest'}</Text>
        </View>
        <IconButton
          icon="logout"
          size={24}
          iconColor={theme.colors.primary}
          onPress={showLogoutDialog}
          style={styles.logoutIcon}
        />
      </View>

      <View style={styles.statsContainer}>
        <Surface style={styles.statCard}>
          <Text style={styles.statNumber}>{stats.totalDoctors}</Text>
          <Text style={styles.statLabel}>Doctors</Text>
        </Surface>
        <Surface style={styles.statCard}>
          <Text style={styles.statNumber}>{stats.totalCounsellors}</Text>
          <Text style={styles.statLabel}>Counsellors</Text>
        </Surface>
        <Surface style={styles.statCard}>
          <Text style={styles.statNumber}>{stats.totalPatients}</Text>
          <Text style={styles.statLabel}>Patients</Text>
        </Surface>
      </View>

      <Card style={styles.card}>
        <Card.Content>
          <Text style={styles.cardTitle}>Staff Management</Text>
          <Button
            mode="contained"
            icon="doctor"
            onPress={() => navigateToStaffManagement('doctor')}
            style={styles.button}
            buttonColor={theme.colors.primary}
          >
            Manage Doctors
          </Button>
          <Button
            mode="contained"
            icon="account-tie"
            onPress={() => navigateToStaffManagement('drug-counsellor')}
            style={styles.button}
            buttonColor={theme.colors.primary}
          >
            Manage Drug Counsellors
          </Button>
        </Card.Content>
      </Card>

      <Card style={styles.card}>
        <Card.Content>
          <Text style={styles.cardTitle}>Patient Management</Text>
          <Button
            mode="contained"
            icon="account"
            onPress={navigateToPatientManagement}
            style={styles.button}
            buttonColor={theme.colors.primary}
          >
            View Patients
          </Button>
        </Card.Content>
      </Card>

      <Card style={styles.card}>
        <Card.Content>
          <Text style={styles.cardTitle}>System Reports</Text>
          <Button
            mode="contained"
            icon="chart-bar"
            onPress={() => navigation.navigate('Analytics')}
            style={styles.button}
            buttonColor={theme.colors.primary}
          >
            View Analytics
          </Button>
        </Card.Content>
      </Card>

      <Card style={[styles.card, styles.lastCard]}>
        <Card.Content>
          <Text style={styles.cardTitle}>System Testing</Text>
          <Button
            mode="contained"
            icon="bell-ring"
            onPress={() => navigation.navigate('NotificationTest')}
            style={styles.button}
            buttonColor={theme.colors.secondary}
          >
            Test Notifications
          </Button>
        </Card.Content>
      </Card>

      {/* Logout Confirmation Dialog */}
      <Portal>
        <Dialog
          visible={logoutDialogVisible}
          onDismiss={hideLogoutDialog}
          style={[styles.dialogContainer, { width: screenWidth > 500 ? 400 : '85%', alignSelf: 'center' }]}
        >
          <View style={styles.iconContainer}>
            <Avatar.Icon
              size={screenWidth < SCREEN_BREAKPOINT_TABLET ? 56 : 64}
              icon="logout"
              color="white"
              style={{ backgroundColor: paperTheme.colors.error }}
            />
          </View>
          <Dialog.Title style={[
            styles.dialogTitle,
            screenWidth < SCREEN_BREAKPOINT_TABLET && { fontSize: 18, marginBottom: theme.spacing.xs }
          ]}>
            Confirm Logout
          </Dialog.Title>
          <Dialog.Content style={styles.dialogContent}>
            <Paragraph style={[
              styles.dialogMessage,
              screenWidth < SCREEN_BREAKPOINT_TABLET && { fontSize: 14, marginBottom: theme.spacing.sm }
            ]}>
              Are you sure you want to log out of the Adverse Drug Reaction Reporting System?
            </Paragraph>
          </Dialog.Content>
          <Dialog.Actions style={[
            styles.dialogActions,
            screenWidth < SCREEN_BREAKPOINT_TABLET && {
              paddingHorizontal: theme.spacing.sm,
              paddingBottom: theme.spacing.sm
            }
          ]}>
            <Button
              onPress={hideLogoutDialog}
              style={[
                styles.cancelButton,
                screenWidth < SCREEN_BREAKPOINT_TABLET && {
                  paddingHorizontal: theme.spacing.sm,
                  minWidth: 80
                }
              ]}
              labelStyle={[
                { color: paperTheme.colors.onSurface },
                screenWidth < SCREEN_BREAKPOINT_TABLET && { fontSize: 12 }
              ]}
              mode="outlined"
            >
              Cancel
            </Button>
            <Button
              onPress={handleLogoutConfirm}
              style={[
                styles.logoutButton,
                screenWidth < SCREEN_BREAKPOINT_TABLET && {
                  paddingHorizontal: theme.spacing.sm,
                  minWidth: 80
                }
              ]}
              mode="contained"
              labelStyle={screenWidth < SCREEN_BREAKPOINT_TABLET ? { fontSize: 12, color: 'white' } : { color: 'white' }}
            >
              Log Out
            </Button>
          </Dialog.Actions>
        </Dialog>
      </Portal>
    </ScrollView>
  );
};

// Remove the responsiveDialog style since we're now applying it directly in the component
const styles = StyleSheet.create({
  container: {
    ...globalStyles.container,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: theme.spacing.lg,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: theme.colors.primary,
  },
  subtitle: {
    fontSize: 16,
    color: theme.colors.text,
    marginTop: theme.spacing.xs,
  },
  logoutIcon: {
    margin: 0,
  },
  statsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: theme.spacing.lg,
  },
  statCard: {
    flex: 1,
    margin: theme.spacing.xs,
    padding: theme.spacing.sm,
    alignItems: 'center',
    borderRadius: theme.roundness,
    elevation: 2,
    backgroundColor: theme.colors.surface,
  },
  statNumber: {
    fontSize: 24,
    fontWeight: 'bold',
    color: theme.colors.primary,
  },
  statLabel: {
    fontSize: 12,
    color: theme.colors.text,
    textAlign: 'center',
    marginTop: theme.spacing.xs,
  },
  card: {
    ...globalStyles.card,
    marginVertical: theme.spacing.sm,
  },
  lastCard: {
    marginBottom: theme.spacing.xl,
  },
  cardHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: theme.spacing.md,
  },
  cardTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: theme.colors.text,
    marginBottom: theme.spacing.sm,
  },
  button: {
    marginVertical: theme.spacing.xs,
  },
  badge: {
    backgroundColor: theme.colors.primary,
    borderRadius: 12,
    paddingHorizontal: 8,
    paddingVertical: 2,
  },
  badgeText: {
    color: theme.colors.surface,
    fontSize: 12,
    fontWeight: 'bold',
  },
  // Logout dialog styles
  dialogContainer: {
    borderRadius: theme.roundness * 2,
    backgroundColor: theme.colors.surface,
    ...globalStyles.shadow,
  },
  dialogTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: theme.colors.primary,
    textAlign: 'center',
    marginBottom: theme.spacing.sm,
  },
  dialogContent: {
    paddingHorizontal: theme.spacing.lg,
    paddingBottom: theme.spacing.md,
  },
  dialogMessage: {
    fontSize: 16,
    color: theme.colors.text,
    textAlign: 'center',
    marginBottom: theme.spacing.md,
  },
  dialogActions: {
    marginTop: theme.spacing.sm,
    paddingHorizontal: theme.spacing.md,
    paddingBottom: theme.spacing.md,
    justifyContent: 'space-between',
  },
  cancelButton: {
    marginRight: theme.spacing.md,
  },
  logoutButton: {
    backgroundColor: theme.colors.error,
  },
  iconContainer: {
    alignItems: 'center',
    marginBottom: theme.spacing.md,
  },
});
