# Push Notification Testing Guide

## Overview
This guide provides step-by-step instructions for testing the newly implemented push notification system in the ADR (Adverse Drug Reaction) application.

## Prerequisites
- Backend server running with the new push notification endpoints
- Frontend app with notification context and services
- Test devices/browsers for different platforms

## Testing Phases

### Phase 1: Backend Testing
1. **Start the backend server**
   ```bash
   cd backend
   npm start
   ```

2. **Verify push notification endpoints**
   - POST `/api/notifications/register-token` - Register push token
   - DELETE `/api/notifications/remove-token` - Remove push token

3. **Test with curl (optional)**
   ```bash
   # Register a token (requires authentication)
   curl -X POST http://localhost:5000/api/notifications/register-token \
     -H "Content-Type: application/json" \
     -H "Authorization: Bearer YOUR_JWT_TOKEN" \
     -d '{"pushToken": "ExponentPushToken[test-token]"}'
   ```

### Phase 2: Frontend Testing

#### Web Platform Testing
1. **Start the development server**
   ```bash
   npm start
   # Select 'w' for web
   ```

2. **Test browser notifications**
   - Navigate to the notification test screen
   - Enable notifications when prompted
   - Test local notifications
   - Verify browser notification permissions

3. **Expected behavior on web:**
   - Browser notification permission prompt
   - Local notifications appear as browser notifications
   - No Expo push tokens (web doesn't support them)
   - Graceful fallback to browser notifications

#### Mobile Platform Testing (Expo Go)
1. **Start the development server**
   ```bash
   npm start
   # Scan QR code with Expo Go app
   ```

2. **Test push notifications**
   - Enable notifications in the app
   - Verify push token registration
   - Test local notifications
   - Test receiving push notifications

3. **Expected behavior on mobile:**
   - Permission request for notifications
   - Expo push token generation
   - Token registration with backend
   - Local and push notifications work

### Phase 3: Integration Testing

#### Chat Message Notifications
1. **Setup two user accounts**
   - One patient account
   - One counsellor account

2. **Test chat notifications**
   - Send message from patient to counsellor
   - Verify counsellor receives push notification
   - Test on both web and mobile platforms

#### Drug Reaction Notifications
1. **Submit a drug reaction report**
   - Login as patient
   - Submit new drug reaction
   - Verify counsellors receive notifications

### Phase 4: Error Handling Testing

#### Permission Denied Scenarios
1. **Test when user denies permissions**
   - Deny notification permissions
   - Verify app continues to work
   - Check error handling and user feedback

#### Network Error Scenarios
1. **Test with backend offline**
   - Stop backend server
   - Try to register for notifications
   - Verify graceful error handling

#### Invalid Token Scenarios
1. **Test with invalid push tokens**
   - Manually test with malformed tokens
   - Verify backend validation

## Testing Checklist

### Web Platform
- [ ] Browser notification permission request works
- [ ] Local notifications appear correctly
- [ ] No crashes when push tokens are unavailable
- [ ] Notification settings UI works
- [ ] Test notification button works
- [ ] Error messages are user-friendly

### Mobile Platform (Expo Go)
- [ ] Push notification permissions work
- [ ] Expo push token generation works
- [ ] Token registration with backend succeeds
- [ ] Local notifications appear correctly
- [ ] Push notifications are received
- [ ] Notification tap handling works

### Cross-Platform
- [ ] App doesn't crash on any platform
- [ ] Notification context provides consistent API
- [ ] Error handling is graceful
- [ ] User can enable/disable notifications
- [ ] Settings persist across app restarts

### Backend Integration
- [ ] Push token registration endpoint works
- [ ] Push token removal endpoint works
- [ ] Chat messages trigger notifications
- [ ] Drug reaction reports trigger notifications
- [ ] Email notifications still work (preserved)

## Rollback Procedures

### If Web Platform Breaks
1. **Quick rollback of App.tsx**
   ```bash
   git checkout HEAD~1 -- App.tsx
   ```

2. **Remove notification imports**
   - Comment out NotificationProvider import
   - Remove from component tree

### If Mobile Platform Breaks
1. **Disable notification features**
   - Set feature flag in environment
   - Gracefully disable notification UI

### If Backend Breaks
1. **Revert backend changes**
   ```bash
   cd backend
   git checkout HEAD~1 -- utils/pushNotifications.js
   git checkout HEAD~1 -- models/User.js
   git checkout HEAD~1 -- index.js
   npm install  # Remove expo-server-sdk if needed
   ```

## Troubleshooting

### Common Issues
1. **"Expo push token not valid" error**
   - Ensure running on physical device
   - Check Expo project configuration

2. **Browser notifications not working**
   - Check browser notification permissions
   - Verify HTTPS in production

3. **Backend token registration fails**
   - Check authentication headers
   - Verify user is logged in
   - Check network connectivity

### Debug Commands
```bash
# Check backend logs
cd backend && npm start

# Check frontend logs
# Open browser developer tools or React Native debugger

# Test notification endpoints directly
curl -X POST http://localhost:5000/api/notifications/register-token \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer TOKEN" \
  -d '{"pushToken": "test"}'
```

## Success Criteria
- [ ] Notifications work on both web and mobile
- [ ] Existing email system continues to work
- [ ] No crashes or blank screens
- [ ] User can easily enable/disable notifications
- [ ] Error handling is user-friendly
- [ ] Performance is not significantly impacted
