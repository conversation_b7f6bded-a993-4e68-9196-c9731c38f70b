import React from 'react';
import { SafeAreaProvider } from 'react-native-safe-area-context';
import { Provider as PaperProvider } from 'react-native-paper';
import { Navigation } from './src/navigation';
import { AuthProvider } from './src/context/AuthContext';
import { DrugReactionProvider } from './src/context/DrugReactionContext';
import { PatientProvider } from './src/context/PatientContext';
import { DrugCounsellingProvider } from './src/context/DrugCounsellingContext'; // Import the DrugCounsellingProvider
import { NotificationProvider } from './src/context/NotificationContext';
import { theme } from './src/theme';

export default function App() {
  return (
    <SafeAreaProvider>
      <PaperProvider theme={theme}>
        <AuthProvider>
          <NotificationProvider>
            <PatientProvider>
              <DrugReactionProvider>
                <DrugCounsellingProvider> {/* Wrap Navigation with DrugCounsellingProvider */}
                  <Navigation />
                </DrugCounsellingProvider>
              </DrugReactionProvider>
            </PatientProvider>
          </NotificationProvider>
        </AuthProvider>
      </PaperProvider>
    </SafeAreaProvider>
  );
}
