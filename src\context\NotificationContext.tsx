import React, { createContext, useContext, useEffect, useState, ReactNode } from 'react';
import { Platform } from 'react-native';
import axios from 'axios';
import { notificationService, showLocalNotification } from '../services/notificationService';
import { useAuth } from './AuthContext';

const API_BASE_URL = process.env.EXPO_PUBLIC_API_URL || 'http://localhost:5000';

interface NotificationContextType {
  isNotificationEnabled: boolean;
  pushToken: string | null;
  enableNotifications: () => Promise<boolean>;
  disableNotifications: () => Promise<void>;
  showNotification: (title: string, body: string, data?: any) => Promise<void>;
  isLoading: boolean;
  error: string | null;
}

const NotificationContext = createContext<NotificationContextType | undefined>(undefined);

interface NotificationProviderProps {
  children: ReactNode;
}

export const NotificationProvider: React.FC<NotificationProviderProps> = ({ children }) => {
  const [isNotificationEnabled, setIsNotificationEnabled] = useState(false);
  const [pushToken, setPushToken] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const { user, token } = useAuth();

  // Initialize notifications when user is authenticated
  useEffect(() => {
    if (user && token) {
      initializeNotifications();
    }
  }, [user, token]);

  const initializeNotifications = async () => {
    try {
      setIsLoading(true);
      setError(null);

      // Check if notifications are already enabled
      const token = await notificationService.getExpoPushToken();
      if (token) {
        setPushToken(token);
        setIsNotificationEnabled(true);
      }
    } catch (error) {
      console.error('Error initializing notifications:', error);
      setError('Failed to initialize notifications');
    } finally {
      setIsLoading(false);
    }
  };

  const enableNotifications = async (): Promise<boolean> => {
    try {
      setIsLoading(true);
      setError(null);

      // Request permissions and get push token
      const pushToken = await notificationService.registerForPushNotifications();

      if (!pushToken) {
        if (Platform.OS === 'web') {
          // For web, we still consider it "enabled" even without a push token
          setIsNotificationEnabled(true);
          return true;
        } else {
          setError('Failed to get push notification token');
          return false;
        }
      }

      // Register push token with backend (only for mobile platforms)
      if (pushToken && user && token) {
        try {
          await axios.post(
            `${API_BASE_URL}/api/notifications/register-token`,
            { pushToken: pushToken },
            {
              headers: {
                Authorization: `Bearer ${token}`,
              },
            }
          );

          setPushToken(pushToken);
          setIsNotificationEnabled(true);
          console.log('Push token registered successfully');
          return true;
        } catch (apiError) {
          console.error('Error registering push token with backend:', apiError);
          setError('Failed to register with server');
          return false;
        }
      }

      return true;
    } catch (error) {
      console.error('Error enabling notifications:', error);
      setError('Failed to enable notifications');
      return false;
    } finally {
      setIsLoading(false);
    }
  };

  const disableNotifications = async (): Promise<void> => {
    try {
      setIsLoading(true);
      setError(null);

      // Remove token from backend
      if (user && token) {
        try {
          await axios.delete(
            `${API_BASE_URL}/api/notifications/remove-token`,
            {
              headers: {
                Authorization: `Bearer ${token}`,
              },
            }
          );
          console.log('Push token removed from backend');
        } catch (apiError) {
          console.error('Error removing push token from backend:', apiError);
          // Continue with local cleanup even if backend fails
        }
      }

      setPushToken(null);
      setIsNotificationEnabled(false);
    } catch (error) {
      console.error('Error disabling notifications:', error);
      setError('Failed to disable notifications');
    } finally {
      setIsLoading(false);
    }
  };

  const showNotification = async (title: string, body: string, data?: any): Promise<void> => {
    try {
      await showLocalNotification(title, body, data);
    } catch (error) {
      console.error('Error showing notification:', error);
    }
  };

  // Set up notification listeners
  useEffect(() => {
    const notificationListener = notificationService.addNotificationListener((notification) => {
      console.log('Notification received:', notification);
      // Handle incoming notification
    });

    const responseListener = notificationService.addNotificationResponseListener((response) => {
      console.log('Notification response:', response);
      // Handle notification tap/interaction
    });

    return () => {
      notificationListener();
      responseListener();
    };
  }, []);

  const value: NotificationContextType = {
    isNotificationEnabled,
    pushToken,
    enableNotifications,
    disableNotifications,
    showNotification,
    isLoading,
    error,
  };

  return (
    <NotificationContext.Provider value={value}>
      {children}
    </NotificationContext.Provider>
  );
};

export const useNotifications = (): NotificationContextType => {
  const context = useContext(NotificationContext);
  if (context === undefined) {
    throw new Error('useNotifications must be used within a NotificationProvider');
  }
  return context;
};
