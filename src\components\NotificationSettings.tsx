import React from 'react';
import { View, StyleSheet, Platform } from 'react-native';
import { Card, Title, Paragraph, Switch, Button, ActivityIndicator } from 'react-native-paper';
import { useNotifications } from '../context/NotificationContext';

export const NotificationSettings: React.FC = () => {
  const {
    isNotificationEnabled,
    enableNotifications,
    disableNotifications,
    isLoading,
    error,
    pushToken,
  } = useNotifications();

  const handleToggleNotifications = async () => {
    if (isNotificationEnabled) {
      await disableNotifications();
    } else {
      await enableNotifications();
    }
  };

  const testNotification = async () => {
    const { showNotification } = useNotifications();
    await showNotification(
      'Test Notification',
      'This is a test notification to verify the system is working.',
      { type: 'test' }
    );
  };

  return (
    <Card style={styles.card}>
      <Card.Content>
        <Title>Push Notifications</Title>
        <Paragraph style={styles.description}>
          {Platform.OS === 'web' 
            ? 'Enable browser notifications to receive updates about new messages and drug reactions.'
            : 'Enable push notifications to receive updates about new messages and drug reactions on your device.'
          }
        </Paragraph>

        <View style={styles.settingRow}>
          <View style={styles.settingText}>
            <Paragraph>Enable Notifications</Paragraph>
            {Platform.OS === 'web' && (
              <Paragraph style={styles.webNote}>
                (Browser notifications only)
              </Paragraph>
            )}
          </View>
          <View style={styles.switchContainer}>
            {isLoading ? (
              <ActivityIndicator size="small" />
            ) : (
              <Switch
                value={isNotificationEnabled}
                onValueChange={handleToggleNotifications}
                disabled={isLoading}
              />
            )}
          </View>
        </View>

        {error && (
          <Paragraph style={styles.error}>
            Error: {error}
          </Paragraph>
        )}

        {isNotificationEnabled && (
          <View style={styles.statusContainer}>
            <Paragraph style={styles.success}>
              ✅ Notifications are enabled
            </Paragraph>
            
            {Platform.OS !== 'web' && pushToken && (
              <Paragraph style={styles.tokenInfo}>
                Device registered for push notifications
              </Paragraph>
            )}

            <Button
              mode="outlined"
              onPress={testNotification}
              style={styles.testButton}
              disabled={isLoading}
            >
              Test Notification
            </Button>
          </View>
        )}

        {!isNotificationEnabled && (
          <Paragraph style={styles.disabledText}>
            Notifications are currently disabled. Enable them to receive real-time updates.
          </Paragraph>
        )}
      </Card.Content>
    </Card>
  );
};

const styles = StyleSheet.create({
  card: {
    margin: 16,
  },
  description: {
    marginBottom: 16,
    color: '#666',
  },
  settingRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  settingText: {
    flex: 1,
  },
  webNote: {
    fontSize: 12,
    color: '#888',
    fontStyle: 'italic',
  },
  switchContainer: {
    marginLeft: 16,
  },
  error: {
    color: '#d32f2f',
    marginBottom: 8,
  },
  success: {
    color: '#2e7d32',
    marginBottom: 8,
  },
  statusContainer: {
    marginTop: 8,
  },
  tokenInfo: {
    fontSize: 12,
    color: '#666',
    marginBottom: 12,
  },
  testButton: {
    marginTop: 8,
  },
  disabledText: {
    color: '#666',
    fontStyle: 'italic',
  },
});
