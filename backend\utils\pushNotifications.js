// utils/pushNotifications.js
const { Expo } = require('expo-server-sdk');
const User = require('../models/User');

// Create a new Expo SDK client
const expo = new Expo();

/**
 * Sends push notifications to multiple users
 * @param {string[]} userIds - Array of user IDs to send notifications to
 * @param {string} title - Notification title
 * @param {string} body - Notification body
 * @param {Object} data - Additional data to send with notification
 * @returns {Promise<void>}
 */
const sendPushNotifications = async (userIds, title, body, data = {}) => {
  try {
    // Fetch users with their push tokens
    const users = await User.find({ 
      _id: { $in: userIds },
      pushToken: { $exists: true, $ne: null }
    });

    if (users.length === 0) {
      console.log('No users found with push tokens');
      return;
    }

    // Create the messages that you want to send to clients
    const messages = [];
    
    for (const user of users) {
      // Check that the push token is valid
      if (!Expo.isExpoPushToken(user.pushToken)) {
        console.error(`Push token ${user.pushToken} is not a valid Expo push token`);
        continue;
      }

      // Construct a message
      messages.push({
        to: user.pushToken,
        sound: 'default',
        title: title,
        body: body,
        data: data,
      });
    }

    // The Expo push notification service accepts batches of notifications
    const chunks = expo.chunkPushNotifications(messages);
    const tickets = [];

    // Send the chunks to the Expo push notification service
    for (const chunk of chunks) {
      try {
        const ticketChunk = await expo.sendPushNotificationsAsync(chunk);
        console.log('Push notification tickets:', ticketChunk);
        tickets.push(...ticketChunk);
      } catch (error) {
        console.error('Error sending push notification chunk:', error);
      }
    }

    // Later, after the Expo push notification service has delivered the
    // notifications to Apple or Google (usually quickly, but allow the service
    // up to 30 minutes when under load), a "receipt" for each notification is
    // created. The receipts will be available for at least a day; stale receipts
    // are deleted.
    //
    // The ID of each receipt is sent back in the response "ticket" for each
    // notification. In summary, sending a notification produces a ticket, which
    // contains a receipt ID you later use to get the receipt.
    //
    // The receipts may contain error codes to which you must respond. In
    // particular, Apple or Google may block apps that continue to send
    // notifications to devices that have blocked notifications or have uninstalled
    // the app. Expo does not control this policy and sends back the feedback from
    // Apple and Google so you can handle it appropriately.
    const receiptIds = [];
    for (const ticket of tickets) {
      // NOTE: Not all tickets have IDs; for example, tickets for notifications
      // that could not be enqueued will have error information and no receipt ID.
      if (ticket.id) {
        receiptIds.push(ticket.id);
      }
    }

    // Later, you can check the receipts to see if there were any errors
    if (receiptIds.length > 0) {
      const receiptIdChunks = expo.chunkPushNotificationReceiptIds(receiptIds);
      
      // Check receipts in the background (don't await to avoid blocking)
      setTimeout(async () => {
        for (const chunk of receiptIdChunks) {
          try {
            const receipts = await expo.getPushNotificationReceiptsAsync(chunk);
            
            // The receipts specify whether Apple or Google successfully received the
            // notification and information about an error, if any.
            for (const receiptId in receipts) {
              const { status, message, details } = receipts[receiptId];
              
              if (status === 'ok') {
                continue;
              } else if (status === 'error') {
                console.error(`There was an error sending a notification: ${message}`);
                
                if (details && details.error) {
                  // The error codes are listed in the Expo documentation:
                  // https://docs.expo.io/push-notifications/sending-notifications/#individual-errors
                  console.error(`The error code is ${details.error}`);
                  
                  // Handle specific errors
                  if (details.error === 'DeviceNotRegistered') {
                    // Remove the invalid push token from the user
                    // This would require additional logic to identify which user
                    console.log('Device not registered, should remove push token');
                  }
                }
              }
            }
          } catch (error) {
            console.error('Error checking push notification receipts:', error);
          }
        }
      }, 15000); // Check receipts after 15 seconds
    }

    console.log(`✅ Push notifications sent to ${messages.length} devices`);
  } catch (error) {
    console.error('❌ Error sending push notifications:', error);
  }
};

/**
 * Registers a push token for a user
 * @param {string} userId - User ID
 * @param {string} pushToken - Expo push token
 * @returns {Promise<boolean>} Success status
 */
const registerPushToken = async (userId, pushToken) => {
  try {
    // Validate the push token
    if (!Expo.isExpoPushToken(pushToken)) {
      console.error(`Push token ${pushToken} is not a valid Expo push token`);
      return false;
    }

    // Update user with push token
    await User.findByIdAndUpdate(userId, { pushToken });
    console.log(`✅ Push token registered for user ${userId}`);
    return true;
  } catch (error) {
    console.error('❌ Error registering push token:', error);
    return false;
  }
};

/**
 * Removes a push token for a user
 * @param {string} userId - User ID
 * @returns {Promise<boolean>} Success status
 */
const removePushToken = async (userId) => {
  try {
    await User.findByIdAndUpdate(userId, { $unset: { pushToken: 1 } });
    console.log(`✅ Push token removed for user ${userId}`);
    return true;
  } catch (error) {
    console.error('❌ Error removing push token:', error);
    return false;
  }
};

module.exports = {
  sendPushNotifications,
  registerPushToken,
  removePushToken
};
